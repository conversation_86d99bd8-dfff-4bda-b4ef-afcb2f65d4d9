#!/usr/bin/env python3
"""
Test to check if there's a session issue causing the attendance loading problem
"""

import sys
sys.path.append('.')
from app import app
import sqlite3

def test_login_and_dashboard_flow():
    """Test the complete login to dashboard flow"""
    print("=== Testing Login to Dashboard Flow ===")
    
    # Get a staff member from database
    conn = sqlite3.connect('vishnorex.db')
    staff = conn.execute('SELECT id, staff_id, full_name FROM staff LIMIT 1').fetchone()
    conn.close()
    
    if not staff:
        print("❌ No staff found in database")
        return False
    
    print(f"Testing with staff: {staff[2]} (ID: {staff[0]}, Staff ID: {staff[1]})")
    
    with app.test_client() as client:
        # Step 1: Try to access staff dashboard without login (should redirect)
        print("\n1. Accessing staff dashboard without login...")
        response = client.get('/staff/dashboard')
        print(f"   Status: {response.status_code} (should be 302 redirect)")
        
        # Step 2: Simulate login by setting session
        print("\n2. Setting up session (simulating login)...")
        with client.session_transaction() as sess:
            sess['user_id'] = staff[0]  # Use database ID
            sess['user_type'] = 'staff'
            sess['full_name'] = staff[2]
            sess['school_id'] = 1
        print("   ✅ Session set up")
        
        # Step 3: Access staff dashboard with session
        print("\n3. Accessing staff dashboard with session...")
        response = client.get('/staff/dashboard')
        print(f"   Status: {response.status_code} (should be 200)")
        
        if response.status_code == 200:
            print("   ✅ Dashboard accessible")
            
            # Check if the response contains the expected elements
            html_content = response.get_data(as_text=True)
            
            checks = [
                ('loadTodayAttendanceStatus', 'JavaScript function call'),
                ('errorContainer', 'Error container'),
                ('staff_dashboard.js', 'JavaScript file inclusion'),
                (staff[2], 'Staff name in content')
            ]
            
            for check, description in checks:
                if check in html_content:
                    print(f"   ✅ {description} found")
                else:
                    print(f"   ❌ {description} missing")
        else:
            print(f"   ❌ Dashboard not accessible: {response.status_code}")
            return False
        
        # Step 4: Test attendance data API with session
        print("\n4. Testing attendance data API with session...")
        response = client.get('/get_today_attendance_status')
        print(f"   Status: {response.status_code} (should be 200)")
        
        if response.status_code == 200:
            data = response.get_json()
            print(f"   Response: {data}")
            
            if data and data.get('success'):
                print("   ✅ API working correctly")
                return True
            else:
                print(f"   ❌ API returned error: {data.get('error') if data else 'No data'}")
                return False
        else:
            print(f"   ❌ API not accessible: {response.status_code}")
            return False

def test_javascript_initialization():
    """Test if JavaScript initialization is correct"""
    print("\n=== Testing JavaScript Initialization ===")
    
    html_file = 'templates/staff_dashboard.html'
    js_file = 'static/js/staff_dashboard.js'
    
    try:
        # Check HTML file
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print("Checking HTML file:")
        
        # Check for DOMContentLoaded
        if 'DOMContentLoaded' in html_content:
            print("  ✅ DOMContentLoaded event listener found")
        else:
            print("  ❌ DOMContentLoaded event listener missing")
            return False
        
        # Check for function call
        if 'loadTodayAttendanceStatus()' in html_content:
            print("  ✅ Function call found")
        else:
            print("  ❌ Function call missing")
            return False
        
        # Check JavaScript file
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print("\nChecking JavaScript file:")
        
        # Check for function definition
        if 'function loadTodayAttendanceStatus()' in js_content:
            print("  ✅ Function definition found")
        else:
            print("  ❌ Function definition missing")
            return False
        
        # Check for fetch call
        if '/get_today_attendance_status' in js_content:
            print("  ✅ API endpoint call found")
        else:
            print("  ❌ API endpoint call missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking files: {e}")
        return False

def check_browser_compatibility():
    """Check for potential browser compatibility issues"""
    print("\n=== Checking Browser Compatibility ===")
    
    js_file = 'static/js/staff_dashboard.js'
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for modern JavaScript features that might not work in older browsers
        modern_features = [
            ('fetch(', 'Fetch API (IE not supported)'),
            ('arrow functions', '=>'),
            ('const ', 'const keyword'),
            ('let ', 'let keyword'),
            ('template literals', '`')
        ]
        
        issues = []
        for feature, pattern in modern_features:
            if pattern in js_content:
                if feature == 'Fetch API (IE not supported)':
                    issues.append(feature)
        
        if issues:
            print("⚠️  Potential browser compatibility issues:")
            for issue in issues:
                print(f"  - {issue}")
            print("  💡 Try testing in Chrome, Firefox, or Edge")
        else:
            print("✅ No obvious browser compatibility issues")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking browser compatibility: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing Session and Authentication Issues")
    print("=" * 60)
    
    results = []
    
    # Run all tests
    results.append(("Login to Dashboard Flow", test_login_and_dashboard_flow()))
    results.append(("JavaScript Initialization", test_javascript_initialization()))
    results.append(("Browser Compatibility", check_browser_compatibility()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SESSION TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\nIf you're still seeing 'Failed to load attendance data':")
        print("1. 🔐 Make sure you're logged in as a staff member")
        print("2. 🌐 Check browser console (F12) for JavaScript errors")
        print("3. 🔄 Try hard refresh (Ctrl+F5)")
        print("4. 🧹 Clear browser cache and cookies")
        print("5. 🔍 Check Network tab to see if API request is being made")
        print("6. 🚫 Try disabling browser extensions")
        print("7. 👤 Try with a different staff account")
        
        print("\n📋 Debugging steps:")
        print("- Open browser dev tools (F12)")
        print("- Go to Console tab")
        print("- Refresh the staff dashboard page")
        print("- Look for any red error messages")
        print("- Check if you see 'loadTodayAttendanceStatus' being called")
        
    else:
        print(f"\n⚠️  {total - passed} issues found")
        print("These need to be fixed first.")
