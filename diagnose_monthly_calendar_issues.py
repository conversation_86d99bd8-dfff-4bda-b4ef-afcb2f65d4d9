#!/usr/bin/env python3
"""
Comprehensive diagnostic for staff monthly calendar issues
"""

import sys
sys.path.append('.')
from app import app
import os
import re

def check_file_structure():
    """Check if all required files exist"""
    print("=== Checking File Structure ===")
    
    required_files = [
        'templates/staff_monthly_calendar.html',
        'static/js/monthly_calendar.js',
        'static/css/styles.css'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_exist = False
    
    return all_exist

def check_html_syntax():
    """Check HTML file for syntax issues"""
    print("\n=== Checking HTML Syntax ===")
    
    try:
        with open('templates/staff_monthly_calendar.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for common HTML issues
        issues = []
        
        # Check for unclosed tags
        if content.count('<script>') != content.count('</script>'):
            issues.append("Mismatched <script> tags")
        
        if content.count('<style>') != content.count('</style>'):
            issues.append("Mismatched <style> tags")
        
        if content.count('<div') != content.count('</div>'):
            issues.append("Mismatched <div> tags")
        
        # Check for required elements
        required_elements = [
            'id="monthlyCalendar"',
            'MonthlyAttendanceCalendar',
            'staff.id',
            'refreshCalendar',
            'printCalendar',
            'goToCurrentMonth'
        ]
        
        for element in required_elements:
            if element not in content:
                issues.append(f"Missing required element: {element}")
        
        if issues:
            print("❌ HTML Issues found:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ HTML syntax appears correct")
            return True
            
    except Exception as e:
        print(f"❌ Error reading HTML file: {e}")
        return False

def check_javascript_syntax():
    """Check JavaScript file for syntax issues"""
    print("\n=== Checking JavaScript Syntax ===")
    
    try:
        with open('static/js/monthly_calendar.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for common JavaScript issues
        issues = []
        
        # Check for class definition
        if 'class MonthlyAttendanceCalendar' not in content:
            issues.append("MonthlyAttendanceCalendar class not found")
        
        # Check for required methods
        required_methods = [
            'constructor',
            'init',
            'render',
            'loadMonthlyData',
            'refresh'
        ]
        
        for method in required_methods:
            if method not in content:
                issues.append(f"Missing method: {method}")
        
        # Check for syntax errors (basic)
        if content.count('{') != content.count('}'):
            issues.append("Mismatched curly braces")
        
        if content.count('(') != content.count(')'):
            issues.append("Mismatched parentheses")
        
        if issues:
            print("❌ JavaScript Issues found:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ JavaScript syntax appears correct")
            return True
            
    except Exception as e:
        print(f"❌ Error reading JavaScript file: {e}")
        return False

def check_route_functionality():
    """Check if the route works correctly"""
    print("\n=== Checking Route Functionality ===")
    
    try:
        with app.test_client() as client:
            # Test without session (should redirect)
            response = client.get('/staff/monthly_calendar')
            if response.status_code == 302:
                print("✅ Route redirects when not logged in")
            else:
                print(f"⚠️  Route returns {response.status_code} when not logged in")
            
            # Test with session
            with client.session_transaction() as sess:
                sess['user_id'] = 40
                sess['user_type'] = 'staff'
                sess['full_name'] = 'Test Staff'
                sess['school_id'] = 1
            
            response = client.get('/staff/monthly_calendar')
            
            if response.status_code == 200:
                print("✅ Route works with valid session")
                
                html_content = response.get_data(as_text=True)
                
                # Check if template variables are rendered
                if '{{ staff.id }}' in html_content:
                    print("❌ Template variables not rendered (Jinja2 issue)")
                    return False
                elif 'staffId:' in html_content:
                    print("✅ Template variables rendered correctly")
                else:
                    print("⚠️  Cannot determine if template variables rendered")
                
                return True
            else:
                print(f"❌ Route returns {response.status_code} with valid session")
                print(f"Response: {response.get_data(as_text=True)[:200]}...")
                return False
                
    except Exception as e:
        print(f"❌ Error testing route: {e}")
        return False

def check_css_issues():
    """Check for CSS-related issues"""
    print("\n=== Checking CSS Issues ===")
    
    try:
        with open('templates/staff_monthly_calendar.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for CSS issues
        issues = []
        
        # Check for inline styles (should be minimal)
        inline_styles = re.findall(r'style="[^"]*"', content)
        if inline_styles:
            issues.append(f"Found {len(inline_styles)} inline styles")
        
        # Check for required CSS classes
        required_classes = [
            'legend-icon',
            'no-print',
            'monthly-calendar-container'
        ]
        
        for css_class in required_classes:
            if css_class not in content:
                issues.append(f"Missing CSS class: {css_class}")
        
        # Check for print media queries
        if '@media print' not in content:
            issues.append("Missing print media queries")
        
        if issues:
            print("⚠️  CSS Issues found:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ CSS appears correct")
            return True
            
    except Exception as e:
        print(f"❌ Error checking CSS: {e}")
        return False

def check_browser_compatibility():
    """Check for browser compatibility issues"""
    print("\n=== Checking Browser Compatibility ===")
    
    try:
        with open('templates/staff_monthly_calendar.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for modern JavaScript features that might cause issues
        modern_features = [
            ('const ', 'ES6 const declarations'),
            ('let ', 'ES6 let declarations'),
            ('class ', 'ES6 classes'),
            ('arrow functions', '=>'),
            ('template literals', '`')
        ]
        
        compatibility_issues = []
        for feature_name, feature_pattern in modern_features:
            if feature_pattern in content:
                print(f"✅ Uses {feature_name} (requires modern browser)")
            else:
                print(f"ℹ️  No {feature_name} found")
        
        # Check for polyfills or fallbacks
        if 'addEventListener' in content:
            print("✅ Uses modern event handling")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking compatibility: {e}")
        return False

def suggest_fixes():
    """Suggest potential fixes based on common issues"""
    print("\n=== Suggested Fixes ===")
    
    print("If you're still experiencing issues, try these solutions:")
    print()
    print("1. 🔄 Clear Browser Cache:")
    print("   - Press Ctrl+Shift+Delete")
    print("   - Clear all cached files")
    print("   - Try in incognito/private mode")
    print()
    print("2. 🌐 Check Browser Console:")
    print("   - Press F12 to open developer tools")
    print("   - Check Console tab for JavaScript errors")
    print("   - Look for 404 errors in Network tab")
    print()
    print("3. 📱 Test Different Browsers:")
    print("   - Try Chrome, Firefox, Edge")
    print("   - Check if issue is browser-specific")
    print()
    print("4. 🔧 Check Server Logs:")
    print("   - Look for Python/Flask errors")
    print("   - Check if templates are being found")
    print()
    print("5. 📂 Verify File Permissions:")
    print("   - Ensure static files are readable")
    print("   - Check template directory permissions")
    print()
    print("6. 🔍 Specific Error Messages:")
    print("   - Share exact error messages you're seeing")
    print("   - Include browser console errors")
    print("   - Note which actions trigger the issues")

if __name__ == "__main__":
    print("🔍 Comprehensive Monthly Calendar Diagnostic")
    print("=" * 60)
    
    results = []
    
    # Run all checks
    results.append(("File Structure", check_file_structure()))
    results.append(("HTML Syntax", check_html_syntax()))
    results.append(("JavaScript Syntax", check_javascript_syntax()))
    results.append(("Route Functionality", check_route_functionality()))
    results.append(("CSS Issues", check_css_issues()))
    results.append(("Browser Compatibility", check_browser_compatibility()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 ALL CHECKS PASSED!")
        print("The file structure and syntax appear correct.")
        print("If you're still having issues, they might be:")
        print("- Browser-specific problems")
        print("- Network/caching issues")
        print("- Server configuration problems")
        print("- Specific user interaction issues")
    else:
        print(f"\n⚠️  {total - passed} issues found")
        print("Please review the failed checks above.")
    
    # Always show suggestions
    suggest_fixes()
