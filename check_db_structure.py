#!/usr/bin/env python3
"""
Check database structure and fix issues
"""

import sqlite3
import datetime

def check_staff_table():
    """Check staff table structure"""
    print("=== Checking Staff Table Structure ===")
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Check staff table structure
        cursor.execute('PRAGMA table_info(staff)')
        columns = cursor.fetchall()
        print('Staff table columns:')
        for col in columns:
            print(f'  {col[1]} {col[2]} (nullable: {not col[3]}, default: {col[4]})')
        
        # Check if created_at column exists
        has_created_at = any(col[1] == 'created_at' for col in columns)
        
        if not has_created_at:
            print("\n❌ ISSUE: created_at column missing from staff table")
            return False
        else:
            print("\n✅ created_at column exists")
        
        # Check recent staff records
        cursor.execute('SELECT id, staff_id, full_name, created_at FROM staff ORDER BY id DESC LIMIT 5')
        recent_staff = cursor.fetchall()
        print(f'\nRecent staff records:')
        for staff in recent_staff:
            print(f'  ID: {staff[0]}, Staff ID: {staff[1]}, Name: {staff[2]}, Created: {staff[3]}')
        
        # Check for NULL created_at values
        cursor.execute('SELECT COUNT(*) FROM staff WHERE created_at IS NULL')
        null_count = cursor.fetchone()[0]
        
        if null_count > 0:
            print(f"\n⚠️  Found {null_count} staff records with NULL created_at")
            return False
        else:
            print(f"\n✅ All staff records have created_at values")
            return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        conn.close()

def fix_null_created_at():
    """Fix NULL created_at values"""
    print("\n=== Fixing NULL created_at Values ===")
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Get staff with NULL created_at
        cursor.execute('SELECT id, staff_id, full_name FROM staff WHERE created_at IS NULL')
        null_staff = cursor.fetchall()
        
        if not null_staff:
            print("✅ No staff with NULL created_at found")
            return True
        
        print(f"Found {len(null_staff)} staff with NULL created_at:")
        
        # Set created_at to current timestamp for NULL records
        current_timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for staff in null_staff:
            print(f"  Fixing: {staff[2]} (ID: {staff[0]}, Staff ID: {staff[1]})")
            cursor.execute('''
                UPDATE staff 
                SET created_at = ? 
                WHERE id = ?
            ''', (current_timestamp, staff[0]))
        
        conn.commit()
        print(f"✅ Fixed {len(null_staff)} staff records")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing created_at: {e}")
        return False
    finally:
        conn.close()

def check_historical_attendance():
    """Check for historical attendance records"""
    print("\n=== Checking Historical Attendance Records ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    
    try:
        # Get all staff with their creation dates
        staff_records = conn.execute('''
            SELECT id, staff_id, full_name, created_at
            FROM staff
            WHERE created_at IS NOT NULL
            ORDER BY created_at DESC
        ''').fetchall()
        
        issues_found = []
        
        for staff in staff_records:
            staff_created = datetime.datetime.strptime(staff['created_at'], '%Y-%m-%d %H:%M:%S').date()
            
            # Check attendance records for this staff
            attendance_records = conn.execute('''
                SELECT date, time_in, time_out, status
                FROM attendance
                WHERE staff_id = ?
                ORDER BY date
            ''', (staff['id'],)).fetchall()
            
            if attendance_records:
                # Check for historical records (before staff creation)
                historical_records = [r for r in attendance_records 
                                    if datetime.datetime.strptime(r['date'], '%Y-%m-%d').date() < staff_created]
                
                if historical_records:
                    issues_found.append({
                        'staff': staff,
                        'staff_created': staff_created,
                        'historical_count': len(historical_records),
                        'total_count': len(attendance_records),
                        'earliest_record': min(historical_records, key=lambda x: x['date'])['date'],
                        'latest_historical': max(historical_records, key=lambda x: x['date'])['date']
                    })
        
        if issues_found:
            print(f"⚠️  Found {len(issues_found)} staff with historical attendance records:")
            for issue in issues_found:
                print(f"\n  Staff: {issue['staff']['full_name']} (ID: {issue['staff']['id']})")
                print(f"    Created: {issue['staff_created']}")
                print(f"    Historical records: {issue['historical_count']}/{issue['total_count']}")
                print(f"    Date range: {issue['earliest_record']} to {issue['latest_historical']}")
            
            return issues_found
        else:
            print("✅ No historical attendance records found")
            return []
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return []
    finally:
        conn.close()

def clean_historical_attendance(issues):
    """Clean up historical attendance records"""
    print(f"\n=== Cleaning Historical Attendance Records ===")
    
    if not issues:
        print("✅ No historical records to clean")
        return True
    
    conn = sqlite3.connect('vishnorex.db')
    
    try:
        total_deleted = 0
        
        for issue in issues:
            staff_id = issue['staff']['id']
            staff_created = issue['staff_created']
            
            print(f"\nCleaning records for {issue['staff']['full_name']}:")
            print(f"  Deleting attendance records before {staff_created}")
            
            # Delete historical attendance records
            cursor = conn.execute('''
                DELETE FROM attendance
                WHERE staff_id = ? AND date < ?
            ''', (staff_id, staff_created.strftime('%Y-%m-%d')))
            
            deleted_count = cursor.rowcount
            total_deleted += deleted_count
            
            print(f"  ✅ Deleted {deleted_count} historical records")
        
        conn.commit()
        print(f"\n✅ Total deleted: {total_deleted} historical attendance records")
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning records: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🔧 Database Structure and Historical Attendance Fix")
    print("=" * 60)
    
    # Step 1: Check staff table structure
    staff_ok = check_staff_table()
    
    # Step 2: Fix NULL created_at values if needed
    if not staff_ok:
        fix_null_created_at()
    
    # Step 3: Check for historical attendance records
    issues = check_historical_attendance()
    
    # Step 4: Clean historical records if found
    if issues:
        print(f"\n🧹 Found {len(issues)} staff with historical attendance records")
        response = input("Do you want to clean these historical records? (y/N): ")
        
        if response.lower() == 'y':
            clean_historical_attendance(issues)
            print("\n✅ Historical attendance records cleaned")
        else:
            print("\n⏭️  Skipped cleaning historical records")
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    if issues:
        print("⚠️  Issues found and addressed:")
        print("1. Staff table structure checked")
        print("2. NULL created_at values fixed")
        print("3. Historical attendance records identified")
        if response.lower() == 'y':
            print("4. Historical records cleaned up")
        
        print("\n🎯 RESULT: New staff will now only have attendance from their creation date")
    else:
        print("✅ No issues found - system is working correctly")
        print("New staff should only get attendance records from their creation date onwards")
