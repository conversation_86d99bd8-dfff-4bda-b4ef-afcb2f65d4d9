<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Calendar - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('admin_dashboard') }}">
                <i class="bi bi-calendar-month"></i> VishnoRex - Monthly Calendar
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin_dashboard') }}">
                            <i class="bi bi-house"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('admin_monthly_calendar') }}">
                            <i class="bi bi-calendar-month"></i> Monthly Calendar
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ session.full_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2><i class="bi bi-calendar-month"></i> Monthly Attendance Calendar</h2>
                <p class="text-muted">View detailed monthly attendance for all staff members</p>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Select Staff Member</h6>
                        <select class="form-select" id="staffSelect">
                            <option value="">Select a staff member...</option>
                            {% for staff in staff_list %}
                            <option value="{{ staff.id }}">{{ staff.full_name }} ({{ staff.staff_id }})</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calendar Container -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div id="monthlyCalendar">
                            <!-- Calendar will be rendered here -->
                            <div class="text-center py-5">
                                <i class="bi bi-calendar-month" style="font-size: 3rem; color: #6c757d;"></i>
                                <h4 class="mt-3 text-muted">Select a staff member to view their monthly attendance</h4>
                                <p class="text-muted">Choose from the dropdown above to get started</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Legend -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-info-circle"></i> Legend</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="me-2" style="font-size: 1.2rem;">🟢</span>
                                    <span>Present</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="me-2" style="font-size: 1.2rem;">⚪</span>
                                    <span>Absent</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="me-2" style="font-size: 1.2rem;">🔵</span>
                                    <span>On Duty</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="me-2" style="font-size: 1.2rem;">🟡</span>
                                    <span>Late</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/monthly_calendar.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let monthlyCalendar = null;
            const staffSelect = document.getElementById('staffSelect');
            
            // Handle staff selection
            staffSelect.addEventListener('change', function() {
                const staffId = this.value;
                
                if (staffId) {
                    // Initialize or update calendar
                    if (monthlyCalendar) {
                        monthlyCalendar.setStaffId(staffId);
                    } else {
                        monthlyCalendar = new MonthlyAttendanceCalendar('monthlyCalendar', {
                            staffId: staffId,
                            isAdminView: true
                        });
                    }
                } else {
                    // Clear calendar
                    document.getElementById('monthlyCalendar').innerHTML = `
                        <div class="text-center py-5">
                            <i class="bi bi-calendar-month" style="font-size: 3rem; color: #6c757d;"></i>
                            <h4 class="mt-3 text-muted">Select a staff member to view their monthly attendance</h4>
                            <p class="text-muted">Choose from the dropdown above to get started</p>
                        </div>
                    `;
                    monthlyCalendar = null;
                }
            });
            
            // Auto-select first staff if only one exists
            if (staffSelect.options.length === 2) { // 1 default + 1 staff
                staffSelect.selectedIndex = 1;
                staffSelect.dispatchEvent(new Event('change'));
            }
        });
    </script>
</body>
</html>
