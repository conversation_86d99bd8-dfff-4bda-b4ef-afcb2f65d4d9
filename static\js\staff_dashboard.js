document.addEventListener('DOMContentLoaded', function() {
    // Helper function to get CSRF token
    function getCSRFToken() {
        const token = document.querySelector('input[name="csrf_token"]');
        return token ? token.value : '';
    }
    // Initialize attendance chart with real data
    const ctx = document.getElementById('attendanceChart')?.getContext('2d');
    if (ctx) {
        fetch('/get_attendance_summary')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const attendanceChart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['Present', 'Absent', 'Late', 'Leave'],
                            datasets: [{
                                data: [data.present, data.absent, data.late, data.leave],
                                backgroundColor: [
                                    '#198754',
                                    '#dc3545',
                                    '#ffc107',
                                    '#0dcaf0'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });

                    // Update counts
                    document.getElementById('presentDays').textContent = data.present;
                    document.getElementById('absentDays').textContent = data.absent;
                    document.getElementById('lateDays').textContent = data.late;
                    document.getElementById('leaveDays').textContent = data.leave;
                }
            });
    }





    // Load today's attendance status on page load
    loadTodayAttendanceStatus();

    function loadTodayAttendanceStatus() {
        // Prevent multiple simultaneous calls
        if (window.attendanceLoading) {
            console.log('⏳ Attendance loading already in progress...');
            return;
        }

        window.attendanceLoading = true;
        console.log('🔄 Loading today\'s attendance status...');

        // Show loading indicator
        showLoadingMessage('Loading attendance data...');

        fetch('/get_today_attendance_status')
            .then(response => {
                console.log('📡 Response received:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('📊 Data received:', data);

                // Clear loading message
                clearMessages();
                window.attendanceLoading = false;

                if (data && data.success) {
                    console.log('✅ Attendance data loaded successfully');
                    updateAttendanceDisplay(data.attendance);
                    updateVerificationHistory(data.verifications);
                    showSuccessMessage('Attendance data loaded successfully');
                    window.dashboardInitialized = true;
                } else {
                    const errorMsg = data ? data.error : 'No data received';
                    console.error('❌ API returned error:', errorMsg);
                    showErrorMessage('Failed to load attendance data: ' + errorMsg);
                }
            })
            .catch(error => {
                console.error('❌ Network/JavaScript error:', error);
                clearMessages();
                window.attendanceLoading = false;

                // Provide specific error messages based on error type
                let userMessage = 'Error loading attendance data. ';

                if (error.message.includes('HTTP 401') || error.message.includes('Unauthorized')) {
                    userMessage += 'Please log in again.';
                } else if (error.message.includes('HTTP 500')) {
                    userMessage += 'Server error. Please try again later.';
                } else if (error.message.includes('Failed to fetch')) {
                    userMessage += 'Network connection issue. Check your internet connection.';
                } else {
                    userMessage += 'Please refresh the page and try again.';
                }

                showErrorMessage(userMessage);
            });
    }

    function showLoadingMessage(message) {
        const errorContainer = document.getElementById('errorContainer');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-info" role="alert">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        ${message}
                    </div>
                </div>
            `;
        }
    }

    function showSuccessMessage(message) {
        const errorContainer = document.getElementById('errorContainer');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // Auto-hide success message after 3 seconds
            setTimeout(() => {
                const alert = errorContainer.querySelector('.alert-success');
                if (alert) {
                    alert.remove();
                }
            }, 3000);
        }
    }

    function showErrorMessage(message) {
        const errorContainer = document.getElementById('errorContainer');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        } else {
            // Fallback: show in console if no error container
            console.error('Error:', message);
            alert('Error: ' + message); // Browser alert as last resort
        }
    }

    function clearMessages() {
        const errorContainer = document.getElementById('errorContainer');
        if (errorContainer) {
            errorContainer.innerHTML = '';
        }
    }

    // Global function for refresh button
    window.refreshAttendanceData = function() {
        console.log('🔄 Manual refresh requested');

        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Refreshing...';
        }

        // Reset loading flag
        window.attendanceLoading = false;

        // Load attendance data
        loadTodayAttendanceStatus();

        // Re-enable button after a delay
        setTimeout(() => {
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
            }
        }, 2000);
    };

    function updateAttendanceDisplay(attendance) {
        console.log('🔄 Updating attendance display with:', attendance);

        try {
            const currentStatus = document.getElementById('currentStatus');
            const timeIn = document.getElementById('timeIn');
            const timeOut = document.getElementById('timeOut');
            const overtimeIn = document.getElementById('overtimeIn');
            const overtimeOut = document.getElementById('overtimeOut');

            // Update today's status widget
            const todayStatusText = document.getElementById('todayStatusText');
            const todayCheckIn = document.getElementById('todayCheckIn');
            const todayCheckOut = document.getElementById('todayCheckOut');

            // Check if required elements exist
            if (!currentStatus || !timeIn || !timeOut) {
                console.warn('⚠️ Some attendance display elements not found in DOM');
            }

        if (attendance) {
            console.log('✅ Displaying attendance data');
            if (timeIn) timeIn.textContent = attendance.time_in || '--:--:--';
            if (timeOut) timeOut.textContent = attendance.time_out || '--:--:--';
            if (overtimeIn) overtimeIn.textContent = attendance.overtime_in || '--:--:--';
            if (overtimeOut) overtimeOut.textContent = attendance.overtime_out || '--:--:--';

            // Update today's status widget
            if (todayCheckIn) todayCheckIn.textContent = attendance.time_in || '--:--';
            if (todayCheckOut) todayCheckOut.textContent = attendance.time_out || '--:--';

            // Update status based on attendance
            let statusText, statusClass, todayStatusClass;
            if (attendance.overtime_out) {
                statusText = 'All Complete';
                statusClass = 'text-success';
                todayStatusClass = 'text-success';
                todayStatusText.textContent = '✅ Day Complete';
            } else if (attendance.overtime_in) {
                statusText = 'Overtime In Progress';
                statusClass = 'text-warning';
                todayStatusClass = 'text-warning';
                todayStatusText.textContent = '⏰ Overtime Active';
            } else if (attendance.time_out) {
                statusText = 'Regular Hours Complete';
                statusClass = 'text-info';
                todayStatusClass = 'text-info';
                todayStatusText.textContent = '✓ Regular Hours Done';
            } else if (attendance.time_in) {
                statusText = 'Checked In';
                statusClass = 'text-primary';
                todayStatusClass = 'text-primary';
                todayStatusText.textContent = '🟢 Checked In';
            } else {
                statusText = 'Not Marked';
                statusClass = 'text-secondary';
                todayStatusClass = 'text-secondary';
                todayStatusText.textContent = '⚪ Not Marked';
            }

            if (currentStatus) {
                currentStatus.textContent = statusText;
                currentStatus.className = statusClass;
            }
            if (todayStatusText) {
                todayStatusText.className = todayStatusClass;
            }
        } else {
            console.log('ℹ️ No attendance data - showing defaults');
            if (currentStatus) {
                currentStatus.textContent = 'Not Marked';
                currentStatus.className = 'text-secondary';
            }
            if (timeIn) timeIn.textContent = '--:--:--';
            if (timeOut) timeOut.textContent = '--:--:--';
            if (overtimeIn) overtimeIn.textContent = '--:--:--';
            if (overtimeOut) overtimeOut.textContent = '--:--:--';

            // Update today's status widget
            if (todayStatusText) {
                todayStatusText.textContent = '⚪ Not Marked';
                todayStatusText.className = 'text-secondary';
            }
            if (todayCheckIn) todayCheckIn.textContent = '--:--';
            if (todayCheckOut) todayCheckOut.textContent = '--:--';
        }

        console.log('✅ Attendance display updated successfully');

        } catch (error) {
            console.error('❌ Error updating attendance display:', error);
            showErrorMessage('Error updating attendance display: ' + error.message);
        }
    }

    function updateVerificationHistory(verifications) {
        console.log('🔄 Updating verification history with:', verifications);

        try {
            const historyBody = document.getElementById('verificationHistory');

            if (!historyBody) {
                console.warn('⚠️ Verification history table not found in DOM');
                return;
            }

            if (!verifications || verifications.length === 0) {
                console.log('ℹ️ No verifications to display');
                historyBody.innerHTML = '<tr><td colspan="3" class="text-center">No verifications today</td></tr>';
                return;
            }

            console.log(`✅ Displaying ${verifications.length} verification records`);

            historyBody.innerHTML = verifications.map(v => {
                try {
                    const time = new Date(v.verification_time).toLocaleTimeString();
                    const statusBadge = v.verification_status === 'success' ?
                        '<span class="badge bg-success">Success</span>' :
                        '<span class="badge bg-danger">Failed</span>';

                    return `
                        <tr>
                            <td>${time}</td>
                            <td>${v.verification_type || 'Unknown'}</td>
                            <td>${statusBadge}</td>
                        </tr>
                    `;
                } catch (rowError) {
                    console.error('❌ Error processing verification row:', rowError, v);
                    return `
                        <tr>
                            <td>--:--</td>
                            <td>Error</td>
                            <td><span class="badge bg-warning">Error</span></td>
                        </tr>
                    `;
                }
            }).join('');

            console.log('✅ Verification history updated successfully');

        } catch (error) {
            console.error('❌ Error updating verification history:', error);
            showErrorMessage('Error updating verification history: ' + error.message);
        }
    }







    // Apply leave
    const submitLeave = document.getElementById('submitLeave');
    submitLeave?.addEventListener('click', function () {
        const leaveType = document.getElementById('leaveType').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const reason = document.getElementById('leaveReason').value;

        if (!leaveType || !startDate || !endDate || !reason) {
            alert('Please fill all fields');
            return;
        }

        fetch('/apply_leave', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `leave_type=${leaveType}&start_date=${startDate}&end_date=${endDate}&reason=${encodeURIComponent(reason)}&csrf_token=${encodeURIComponent(getCSRFToken())}`
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Leave application submitted successfully');
                    bootstrap.Modal.getInstance(document.getElementById('applyLeaveModal')).hide();
                    location.reload();
                } else {
                    alert(data.error || 'Failed to submit leave application');
                }
            });
    });

    // Apply on duty
    const submitOnDuty = document.getElementById('submitOnDuty');
    submitOnDuty?.addEventListener('click', function () {
        const form = document.getElementById('onDutyForm');
        const formData = new FormData(form);

        // Basic validation
        const dutyType = formData.get('duty_type');
        const startDate = formData.get('start_date');
        const endDate = formData.get('end_date');
        const purpose = formData.get('purpose');

        if (!dutyType || !startDate || !endDate || !purpose) {
            alert('Please fill all required fields');
            return;
        }

        // Check if end date is after start date
        if (new Date(endDate) < new Date(startDate)) {
            alert('End date must be after or equal to start date');
            return;
        }

        fetch('/apply_on_duty', {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message || 'On-duty application submitted successfully');
                    bootstrap.Modal.getInstance(document.getElementById('applyOnDutyModal')).hide();
                    form.reset();
                    location.reload();
                } else {
                    alert(data.error || 'Failed to submit on-duty application');
                }
            })
            .catch(error => {
                console.error('Error submitting on-duty application:', error);
                alert('Error submitting on-duty application');
            });
    });

    // Apply permission
    const submitPermission = document.getElementById('submitPermission');
    submitPermission?.addEventListener('click', function () {
        const form = document.getElementById('permissionForm');
        const formData = new FormData(form);

        // Basic validation
        const permissionType = formData.get('permission_type');
        const permissionDate = formData.get('permission_date');
        const startTime = formData.get('start_time');
        const endTime = formData.get('end_time');
        const reason = formData.get('reason');

        if (!permissionType || !permissionDate || !startTime || !endTime || !reason) {
            alert('Please fill all required fields');
            return;
        }

        // Check if end time is after start time
        if (startTime >= endTime) {
            alert('End time must be after start time');
            return;
        }

        fetch('/apply_permission', {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message || 'Permission application submitted successfully');
                    bootstrap.Modal.getInstance(document.getElementById('applyPermissionModal')).hide();
                    form.reset();
                    location.reload();
                } else {
                    alert(data.error || 'Failed to submit permission application');
                }
            })
            .catch(error => {
                console.error('Error submitting permission application:', error);
                alert('Error submitting permission application');
            });
    });

    // Updated download report with date selection
// Updated download report with date selection
document.getElementById('downloadReportBtn')?.addEventListener('click', function() {
    const startDate = prompt('Enter start date (YYYY-MM-DD):');
    if (!startDate) return;

    const endDate = prompt('Enter end date (YYYY-MM-DD):');
    if (!endDate) return;

    fetch(`/export_staff_report?start_date=${startDate}&end_date=${endDate}`)
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `attendance_report_${startDate}_to_${endDate}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        });
});



    function formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }









});
