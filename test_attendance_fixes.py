#!/usr/bin/env python3
"""
Test the attendance fixes to ensure new staff don't get historical records
"""

import sys
sys.path.append('.')
from app import app, validate_attendance_date
import sqlite3
import datetime
import json

def test_attendance_validation():
    """Test the attendance date validation function"""
    print("=== Testing Attendance Date Validation ===")
    
    with app.test_client() as client:
        with client.session_transaction() as sess:
            sess['user_id'] = 1
            sess['user_type'] = 'admin'
            sess['school_id'] = 1
        
        # Test with a real staff member
        conn = sqlite3.connect('vishnorex.db')
        conn.row_factory = sqlite3.Row
        
        # Get a staff member with created_at
        staff = conn.execute('''
            SELECT id, staff_id, full_name, created_at
            FROM staff
            WHERE created_at IS NOT NULL
            ORDER BY id DESC
            LIMIT 1
        ''').fetchone()
        
        if not staff:
            print("❌ No staff with created_at found")
            return False
        
        print(f"Testing with staff: {staff['full_name']} (ID: {staff['id']})")
        print(f"Created at: {staff['created_at']}")
        
        staff_created = datetime.datetime.strptime(staff['created_at'], '%Y-%m-%d %H:%M:%S').date()
        
        # Test 1: Valid date (today)
        today = datetime.date.today()
        with app.app_context():
            result = validate_attendance_date(staff['id'], today)
        
        if result is None:
            print(f"✅ Valid date test passed: {today}")
        else:
            print(f"❌ Valid date test failed: {result}")
            return False
        
        # Test 2: Invalid date (before creation)
        yesterday = staff_created - datetime.timedelta(days=1)
        with app.app_context():
            result = validate_attendance_date(staff['id'], yesterday)
        
        if result and "Cannot create attendance record" in result:
            print(f"✅ Invalid date test passed: {yesterday} rejected")
        else:
            print(f"❌ Invalid date test failed: {result}")
            return False
        
        # Test 3: Edge case - creation date itself
        with app.app_context():
            result = validate_attendance_date(staff['id'], staff_created)
        
        if result is None:
            print(f"✅ Creation date test passed: {staff_created} allowed")
        else:
            print(f"❌ Creation date test failed: {result}")
            return False
        
        conn.close()
        return True

def test_new_staff_creation():
    """Test creating a new staff member and ensure no historical records"""
    print("\n=== Testing New Staff Creation ===")
    
    with app.test_client() as client:
        with client.session_transaction() as sess:
            sess['user_id'] = 1
            sess['user_type'] = 'admin'
            sess['school_id'] = 1
        
        # Create a test staff member
        test_staff_id = f"TEST{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Disable CSRF for testing
        app.config['WTF_CSRF_ENABLED'] = False

        response = client.post('/add_staff', data={
            'staff_id': test_staff_id,
            'full_name': 'Test Staff Member',
            'password': 'testpass123',
            'email': '<EMAIL>',
            'phone': '1234567890',
            'department': 'Testing',
            'position': 'Test Position'
        })

        # Re-enable CSRF
        app.config['WTF_CSRF_ENABLED'] = True
        
        if response.status_code != 200:
            print(f"❌ Failed to create staff: {response.status_code}")
            return False
        
        data = response.get_json()
        if not data.get('success'):
            print(f"❌ Staff creation failed: {data.get('error')}")
            return False
        
        print(f"✅ Created test staff: {test_staff_id}")
        
        # Get the created staff
        conn = sqlite3.connect('vishnorex.db')
        conn.row_factory = sqlite3.Row
        
        new_staff = conn.execute('''
            SELECT id, staff_id, full_name, created_at
            FROM staff
            WHERE staff_id = ?
        ''', (test_staff_id,)).fetchone()
        
        if not new_staff:
            print("❌ Created staff not found in database")
            conn.close()
            return False
        
        print(f"Staff ID in DB: {new_staff['id']}, Created: {new_staff['created_at']}")
        
        # Check for any attendance records
        attendance_records = conn.execute('''
            SELECT date, time_in, time_out, status
            FROM attendance
            WHERE staff_id = ?
            ORDER BY date
        ''', (new_staff['id'],)).fetchall()
        
        if attendance_records:
            print(f"❌ New staff has {len(attendance_records)} attendance records (should be 0)")
            for record in attendance_records:
                print(f"  - {record['date']}: {record['status']}")
            conn.close()
            return False
        else:
            print("✅ New staff has no attendance records (correct)")
        
        # Clean up - delete the test staff
        conn.execute('DELETE FROM staff WHERE id = ?', (new_staff['id'],))
        conn.commit()
        conn.close()
        
        return True

def test_historical_cleanup():
    """Test that historical records were cleaned up"""
    print("\n=== Testing Historical Record Cleanup ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    
    # Check all staff for historical records
    staff_records = conn.execute('''
        SELECT id, staff_id, full_name, created_at
        FROM staff
        WHERE created_at IS NOT NULL
    ''').fetchall()
    
    issues_found = 0
    
    for staff in staff_records:
        staff_created = datetime.datetime.strptime(staff['created_at'], '%Y-%m-%d %H:%M:%S').date()
        
        # Check for historical attendance records
        historical_records = conn.execute('''
            SELECT COUNT(*) as count
            FROM attendance
            WHERE staff_id = ? AND date < ?
        ''', (staff['id'], staff_created.strftime('%Y-%m-%d'))).fetchone()
        
        if historical_records['count'] > 0:
            print(f"❌ {staff['full_name']} still has {historical_records['count']} historical records")
            issues_found += 1
    
    conn.close()
    
    if issues_found == 0:
        print("✅ No historical attendance records found")
        return True
    else:
        print(f"❌ Found {issues_found} staff with historical records")
        return False

def test_warnings_fixed():
    """Test that app.py warnings are fixed"""
    print("\n=== Testing Warning Fixes ===")
    
    # Test that the app can start without deprecation warnings
    try:
        with app.test_client() as client:
            response = client.get('/')
            if response.status_code == 200:
                print("✅ App starts without errors")
                return True
            else:
                print(f"❌ App returned status {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ App failed to start: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Attendance System Fixes")
    print("=" * 60)
    
    results = []
    
    # Run all tests
    results.append(("Attendance Date Validation", test_attendance_validation()))
    results.append(("New Staff Creation", test_new_staff_creation()))
    results.append(("Historical Record Cleanup", test_historical_cleanup()))
    results.append(("Warning Fixes", test_warnings_fixed()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL FIXES WORKING CORRECTLY!")
        print("\n✅ What's been fixed:")
        print("1. ✅ SQLite date adapter deprecation warnings resolved")
        print("2. ✅ Staff creation now properly sets created_at timestamp")
        print("3. ✅ Historical attendance records cleaned up")
        print("4. ✅ Validation prevents future historical records")
        print("5. ✅ New staff only get attendance from creation date onwards")
        
        print("\n🚀 The attendance system is now working correctly!")
        print("New staff will only have attendance records from their registration date.")
        
    else:
        print(f"\n⚠️  {total - passed} issues still need attention")
        print("Please check the failed tests above.")
