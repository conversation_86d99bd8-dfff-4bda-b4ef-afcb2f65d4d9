<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Monthly Calendar - Staff Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('staff_dashboard') }}">
                <i class="bi bi-calendar-month"></i> VishnoRex - My Monthly Calendar
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('staff_dashboard') }}">
                            <i class="bi bi-house"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('staff_monthly_calendar') }}">
                            <i class="bi bi-calendar-month"></i> Monthly Calendar
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ staff.full_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2><i class="bi bi-calendar-month"></i> My Monthly Attendance Calendar</h2>
                <p class="text-muted">View your detailed monthly attendance history</p>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Staff Information</h6>
                        <p class="mb-1"><strong>Name:</strong> {{ staff.full_name }}</p>
                        <p class="mb-1"><strong>ID:</strong> {{ staff.staff_id }}</p>
                        <p class="mb-0"><strong>Shift:</strong> {{ staff.shift_type|title if staff.shift_type else 'General' }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calendar Container -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div id="monthlyCalendar">
                            <!-- Calendar will be rendered here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Legend -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-info-circle"></i> Legend</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="me-2" style="font-size: 1.2rem;">🟢</span>
                                    <span>Present</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="me-2" style="font-size: 1.2rem;">⚪</span>
                                    <span>Absent</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="me-2" style="font-size: 1.2rem;">🔵</span>
                                    <span>On Duty</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="me-2" style="font-size: 1.2rem;">🟡</span>
                                    <span>Late</span>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="bi bi-info-circle"></i>
                                    Click on any day to see detailed attendance information including check-in/out times and on-duty details.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="{{ url_for('staff_dashboard') }}" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="bi bi-house"></i> Back to Dashboard
                                </a>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-success w-100 mb-2" onclick="refreshCalendar()">
                                    <i class="bi bi-arrow-clockwise"></i> Refresh Calendar
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info w-100 mb-2" onclick="goToCurrentMonth()">
                                    <i class="bi bi-calendar-today"></i> Current Month
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-secondary w-100 mb-2" onclick="printCalendar()">
                                    <i class="bi bi-printer"></i> Print Calendar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/monthly_calendar.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize monthly calendar for current staff
            const monthlyCalendar = new MonthlyAttendanceCalendar('monthlyCalendar', {
                staffId: {{ staff.id }},
                isAdminView: false
            });
            
            // Store reference for global functions
            window.monthlyCalendar = monthlyCalendar;
        });
        
        // Quick action functions
        function refreshCalendar() {
            if (window.monthlyCalendar) {
                window.monthlyCalendar.refresh();
            }
        }
        
        function goToCurrentMonth() {
            if (window.monthlyCalendar) {
                const now = new Date();
                window.monthlyCalendar.goToMonth(now.getFullYear(), now.getMonth() + 1);
            }
        }
        
        function printCalendar() {
            // Hide non-essential elements for printing
            const elementsToHide = document.querySelectorAll('.navbar, .card:not(.card:has(#monthlyCalendar))');
            elementsToHide.forEach(el => el.style.display = 'none');
            
            // Print
            window.print();
            
            // Restore elements
            elementsToHide.forEach(el => el.style.display = '');
        }
    </script>
</body>
</html>
