#!/usr/bin/env python3
"""
Final comprehensive test for attendance loading functionality
"""

import sys
import os
sys.path.append('.')

from app import app
import json

def test_complete_flow():
    """Test the complete attendance loading flow"""
    print("=== Testing Complete Attendance Loading Flow ===")
    
    with app.test_client() as client:
        # Set up session
        with client.session_transaction() as sess:
            sess['user_id'] = 40
            sess['user_type'] = 'staff'
            sess['full_name'] = 'Test Staff'
            sess['school_id'] = 1
        
        # Test the attendance route
        response = client.get('/get_today_attendance_status')
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.get_json()
            
            if data and data.get('success'):
                print("✅ Backend route working correctly")
                print(f"  - Success: {data.get('success')}")
                print(f"  - Attendance: {data.get('attendance')}")
                print(f"  - Verifications: {len(data.get('verifications', []))} records")
                print(f"  - Available Actions: {data.get('available_actions')}")
                
                # Check data structure
                if 'attendance' in data and 'verifications' in data and 'available_actions' in data:
                    print("✅ Response has all required fields")
                    return True
                else:
                    print("❌ Response missing required fields")
                    return False
            else:
                print(f"❌ Backend returned error: {data.get('error') if data else 'Unknown error'}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False

def check_files_integrity():
    """Check that all files have the correct content"""
    print("\n=== Checking Files Integrity ===")
    
    files_to_check = [
        ('templates/staff_dashboard.html', [
            'id="errorContainer"',
            'loadTodayAttendanceStatus',
            'DOMContentLoaded'
        ]),
        ('static/js/staff_dashboard.js', [
            'function loadTodayAttendanceStatus()',
            'function showErrorMessage(',
            '/get_today_attendance_status'
        ]),
        ('app.py', [
            '@app.route(\'/get_today_attendance_status\')',
            'try:',
            'except Exception as e:'
        ])
    ]
    
    all_good = True
    
    for file_path, required_content in files_to_check:
        print(f"\nChecking {file_path}:")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for required in required_content:
                if required in content:
                    print(f"  ✅ {required}")
                else:
                    print(f"  ❌ {required}")
                    all_good = False
                    
        except Exception as e:
            print(f"  ❌ Error reading file: {e}")
            all_good = False
    
    return all_good

def simulate_browser_request():
    """Simulate what happens when browser loads the page"""
    print("\n=== Simulating Browser Request ===")
    
    with app.test_client() as client:
        # Set up session
        with client.session_transaction() as sess:
            sess['user_id'] = 40
            sess['user_type'] = 'staff'
            sess['full_name'] = 'Test Staff'
            sess['school_id'] = 1
        
        # 1. Browser loads the staff dashboard page
        print("1. Loading staff dashboard page...")
        dashboard_response = client.get('/staff/dashboard')
        
        if dashboard_response.status_code == 200:
            print("   ✅ Dashboard page loaded successfully")
        else:
            print(f"   ❌ Dashboard page failed: {dashboard_response.status_code}")
            return False
        
        # 2. JavaScript makes AJAX request for attendance data
        print("2. JavaScript requesting attendance data...")
        ajax_response = client.get('/get_today_attendance_status')
        
        if ajax_response.status_code == 200:
            data = ajax_response.get_json()
            if data and data.get('success'):
                print("   ✅ AJAX request successful")
                print(f"   📊 Data received: {json.dumps(data, indent=6)}")
                return True
            else:
                print(f"   ❌ AJAX request failed: {data.get('error') if data else 'No data'}")
                return False
        else:
            print(f"   ❌ AJAX request HTTP error: {ajax_response.status_code}")
            return False

if __name__ == "__main__":
    print("🔍 Final Attendance Loading Test")
    print("=" * 60)
    
    results = []
    
    # Run all tests
    results.append(("Complete Flow Test", test_complete_flow()))
    results.append(("Files Integrity Check", check_files_integrity()))
    results.append(("Browser Request Simulation", simulate_browser_request()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FINAL TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ATTENDANCE LOADING IS FULLY FIXED!")
        print("\n✅ What's working:")
        print("1. Backend route returns correct data")
        print("2. JavaScript function exists and is called")
        print("3. HTML has error container and function call")
        print("4. Complete request flow works end-to-end")
        
        print("\n🚀 The staff dashboard should now load attendance data correctly!")
        print("\nIf you still see issues:")
        print("- Clear browser cache (Ctrl+Shift+Delete)")
        print("- Hard refresh (Ctrl+F5)")
        print("- Check browser console for any remaining errors")
        print("- Try in incognito/private mode")
        
    else:
        print(f"\n⚠️  {total - passed} issues still need attention")
        print("Please check the failed tests above.")
