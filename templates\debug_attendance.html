<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Attendance Loading</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-4">
        <h2>🔍 Attendance Loading Debug Page</h2>
        <p class="text-muted">This page helps diagnose attendance loading issues</p>

        <!-- Error Container -->
        <div id="errorContainer"></div>

        <!-- Debug Information -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🧪 Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p>Running tests...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 API Response</h5>
                    </div>
                    <div class="card-body">
                        <div id="apiResponse">
                            <p>No API call made yet</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Manual Test Button -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>🔧 Manual Tests</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testAttendanceAPI()">
                            <i class="bi bi-play-circle"></i> Test Attendance API
                        </button>
                        <button class="btn btn-secondary ms-2" onclick="testJavaScriptFunctions()">
                            <i class="bi bi-code"></i> Test JavaScript Functions
                        </button>
                        <button class="btn btn-info ms-2" onclick="showBrowserInfo()">
                            <i class="bi bi-info-circle"></i> Browser Info
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Console Output -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>📝 Console Output</h5>
                    </div>
                    <div class="card-body">
                        <pre id="consoleOutput" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px;"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Console logging override to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌ ERROR' : '📝 LOG';
            consoleOutput.textContent += `[${timestamp}] ${prefix}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // Error display function
        function showErrorMessage(message) {
            const errorContainer = document.getElementById('errorContainer');
            errorContainer.innerHTML = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        // Test attendance API
        function testAttendanceAPI() {
            console.log('Testing attendance API...');
            
            fetch('/get_today_attendance_status')
                .then(response => {
                    console.log(`Response status: ${response.status}`);
                    console.log(`Response ok: ${response.ok}`);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('API Response received:', JSON.stringify(data, null, 2));
                    
                    document.getElementById('apiResponse').innerHTML = `
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                    if (data.success) {
                        console.log('✅ API call successful');
                        updateTestResults('API Test', 'PASS', 'API returned success response');
                    } else {
                        console.error('❌ API returned error:', data.error);
                        updateTestResults('API Test', 'FAIL', data.error);
                        showErrorMessage('API Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('❌ API call failed:', error);
                    updateTestResults('API Test', 'FAIL', error.message);
                    showErrorMessage('API Call Failed: ' + error.message);
                    
                    document.getElementById('apiResponse').innerHTML = `
                        <div class="alert alert-danger">
                            <strong>Error:</strong> ${error.message}
                        </div>
                    `;
                });
        }

        // Test JavaScript functions
        function testJavaScriptFunctions() {
            console.log('Testing JavaScript functions...');
            
            const tests = [
                {
                    name: 'Fetch API Support',
                    test: () => typeof fetch === 'function',
                    description: 'Browser supports fetch API'
                },
                {
                    name: 'JSON Support',
                    test: () => typeof JSON === 'object' && typeof JSON.parse === 'function',
                    description: 'Browser supports JSON parsing'
                },
                {
                    name: 'Promise Support',
                    test: () => typeof Promise === 'function',
                    description: 'Browser supports Promises'
                },
                {
                    name: 'DOM Ready',
                    test: () => document.readyState === 'complete' || document.readyState === 'interactive',
                    description: 'DOM is ready'
                }
            ];
            
            tests.forEach(test => {
                try {
                    const result = test.test();
                    const status = result ? 'PASS' : 'FAIL';
                    console.log(`${status}: ${test.name} - ${test.description}`);
                    updateTestResults(test.name, status, test.description);
                } catch (error) {
                    console.error(`ERROR: ${test.name} - ${error.message}`);
                    updateTestResults(test.name, 'ERROR', error.message);
                }
            });
        }

        // Show browser information
        function showBrowserInfo() {
            console.log('Gathering browser information...');
            
            const info = {
                'User Agent': navigator.userAgent,
                'Browser Language': navigator.language,
                'Platform': navigator.platform,
                'Cookies Enabled': navigator.cookieEnabled,
                'Online': navigator.onLine,
                'Screen Resolution': `${screen.width}x${screen.height}`,
                'Viewport Size': `${window.innerWidth}x${window.innerHeight}`,
                'Document Ready State': document.readyState,
                'Current URL': window.location.href
            };
            
            console.log('Browser Information:');
            Object.entries(info).forEach(([key, value]) => {
                console.log(`  ${key}: ${value}`);
            });
            
            updateTestResults('Browser Info', 'INFO', 'Check console for details');
        }

        // Update test results display
        function updateTestResults(testName, status, description) {
            const testResults = document.getElementById('testResults');
            const statusClass = status === 'PASS' ? 'success' : status === 'FAIL' ? 'danger' : 'info';
            const icon = status === 'PASS' ? 'check-circle' : status === 'FAIL' ? 'x-circle' : 'info-circle';
            
            const resultHtml = `
                <div class="alert alert-${statusClass} py-2 mb-2">
                    <i class="bi bi-${icon}"></i> <strong>${testName}:</strong> ${status}
                    <br><small>${description}</small>
                </div>
            `;
            
            if (testResults.innerHTML.includes('Running tests...')) {
                testResults.innerHTML = resultHtml;
            } else {
                testResults.innerHTML += resultHtml;
            }
        }

        // Auto-run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 Debug page loaded');
            console.log('Running automatic tests...');
            
            // Wait a moment for everything to load
            setTimeout(() => {
                testJavaScriptFunctions();
                showBrowserInfo();
                
                // Auto-test API if we're logged in
                setTimeout(() => {
                    testAttendanceAPI();
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
