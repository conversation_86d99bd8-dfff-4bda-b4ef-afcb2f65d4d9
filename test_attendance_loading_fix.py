#!/usr/bin/env python3
"""
Test to verify that the attendance data loading issue is fixed
"""

import sqlite3
import datetime
import json

def test_attendance_route():
    """Test the get_today_attendance_status route"""
    print("=== Testing Attendance Route ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get a staff member
        cursor.execute('SELECT id, staff_id, full_name FROM staff LIMIT 1')
        staff = cursor.fetchone()
        
        if not staff:
            print("❌ No staff found in database")
            return False
        
        staff_id = staff['id']
        today = datetime.date.today()
        
        print(f"Testing with staff: {staff['full_name']} (ID: {staff_id})")
        
        # Test the attendance query
        cursor.execute('''
            SELECT time_in, time_out, overtime_in, overtime_out, status
            FROM attendance
            WHERE staff_id = ? AND date = ?
        ''', (staff_id, today))
        
        attendance = cursor.fetchone()
        print(f"✅ Attendance query executed successfully")
        
        if attendance:
            print(f"  - Found attendance record for today")
            print(f"  - Time in: {attendance['time_in']}")
            print(f"  - Time out: {attendance['time_out']}")
        else:
            print(f"  - No attendance record for today")
        
        # Test the verifications query (without biometric_method)
        cursor.execute('''
            SELECT verification_type, verification_time, verification_status
            FROM biometric_verifications
            WHERE staff_id = ? AND DATE(verification_time) = ?
            ORDER BY verification_time DESC
        ''', (staff_id, today))
        
        verifications = cursor.fetchall()
        print(f"✅ Verifications query executed successfully")
        print(f"  - Found {len(verifications)} verification records")
        
        return True
        
    except Exception as e:
        print(f"❌ Database query error: {e}")
        return False
    finally:
        conn.close()

def test_javascript_function_references():
    """Test that JavaScript doesn't reference removed functions"""
    print("\n=== Testing JavaScript Function References ===")
    
    js_file = 'static/js/staff_dashboard.js'
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check that removed function is not called
        if 'updateAvailableActions(' in content:
            print("❌ Found reference to removed updateAvailableActions function")
            return False
        
        # Check that loadTodayAttendanceStatus exists
        if 'function loadTodayAttendanceStatus()' not in content:
            print("❌ loadTodayAttendanceStatus function not found")
            return False
        
        # Check that error handling is present
        if 'showErrorMessage' not in content:
            print("❌ Error handling function not found")
            return False
        
        print("✅ JavaScript function references are correct")
        return True
        
    except Exception as e:
        print(f"❌ Error reading JavaScript file: {e}")
        return False

def test_error_container_exists():
    """Test that error container exists in HTML"""
    print("\n=== Testing Error Container ===")
    
    html_file = 'templates/staff_dashboard.html'
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'id="errorContainer"' in content:
            print("✅ Error container found in HTML")
            return True
        else:
            print("❌ Error container not found in HTML")
            return False
        
    except Exception as e:
        print(f"❌ Error reading HTML file: {e}")
        return False

def test_route_error_handling():
    """Test that the route has proper error handling"""
    print("\n=== Testing Route Error Handling ===")
    
    app_file = 'app.py'
    
    try:
        with open(app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check that the route has try-catch
        route_section = content[content.find('@app.route(\'/get_today_attendance_status\')'):content.find('@app.route(\'/get_today_attendance_status\')')+2000]
        
        if 'try:' in route_section and 'except Exception as e:' in route_section:
            print("✅ Route has proper error handling")
            
            # Check that biometric_method is not referenced
            if 'biometric_method' not in route_section:
                print("✅ biometric_method reference removed from route")
                return True
            else:
                print("❌ biometric_method still referenced in route")
                return False
        else:
            print("❌ Route missing error handling")
            return False
        
    except Exception as e:
        print(f"❌ Error reading app.py file: {e}")
        return False

def test_database_schema():
    """Test database schema to understand table structure"""
    print("\n=== Testing Database Schema ===")
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Check attendance table structure
        cursor.execute("PRAGMA table_info(attendance)")
        attendance_columns = cursor.fetchall()
        
        print("Attendance table columns:")
        for col in attendance_columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # Check biometric_verifications table structure
        cursor.execute("PRAGMA table_info(biometric_verifications)")
        verification_columns = cursor.fetchall()
        
        print("\nBiometric_verifications table columns:")
        for col in verification_columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # Check if biometric_method column exists
        has_biometric_method = any(col[1] == 'biometric_method' for col in verification_columns)
        
        if has_biometric_method:
            print("✅ biometric_method column exists in database")
        else:
            print("⚠️  biometric_method column does not exist in database")
        
        return True
        
    except Exception as e:
        print(f"❌ Database schema error: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🔧 Testing Attendance Data Loading Fix")
    print("=" * 60)
    
    results = []
    
    # Run all tests
    results.append(("Database Schema Check", test_database_schema()))
    results.append(("Attendance Route Test", test_attendance_route()))
    results.append(("JavaScript Function References", test_javascript_function_references()))
    results.append(("Error Container Exists", test_error_container_exists()))
    results.append(("Route Error Handling", test_route_error_handling()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ATTENDANCE DATA LOADING ISSUE FIXED!")
        print("\nFixes implemented:")
        print("1. ✅ Removed call to non-existent updateAvailableActions function")
        print("2. ✅ Added proper error handling to backend route")
        print("3. ✅ Removed biometric_method from database query")
        print("4. ✅ Added error display container to HTML")
        print("5. ✅ Enhanced JavaScript error handling with user feedback")
        print("6. ✅ Added try-catch blocks for better debugging")
        
        print("\n🚀 Staff dashboard should now load attendance data correctly!")
        print("\nIf you still see 'Failed to load attendance data':")
        print("- Check browser console for specific error messages")
        print("- Verify database connection and table structure")
        print("- Ensure staff is logged in with valid session")
    else:
        print(f"\n⚠️  {total - passed} issues need attention")
        print("Please check the failed tests above.")
