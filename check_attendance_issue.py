#!/usr/bin/env python3
"""
Check for historical attendance records being created for new staff
"""

import sqlite3
import datetime

def check_attendance_issue():
    """Check if new staff are getting historical attendance records"""
    print("=== Checking Attendance Issue ===")
    
    conn = sqlite3.connect('vishnorex.db')
    conn.row_factory = sqlite3.Row
    
    try:
        # Get the most recent staff member
        recent_staff = conn.execute('''
            SELECT id, staff_id, full_name, created_at 
            FROM staff 
            ORDER BY created_at DESC 
            LIMIT 1
        ''').fetchone()
        
        if not recent_staff:
            print("❌ No staff found in database")
            return
        
        print(f"Most recent staff: {recent_staff['full_name']} (ID: {recent_staff['id']}, Staff ID: {recent_staff['staff_id']})")
        print(f"Created at: {recent_staff['created_at']}")
        
        # Parse creation date
        staff_created = datetime.datetime.strptime(recent_staff['created_at'], '%Y-%m-%d %H:%M:%S').date()
        print(f"Staff creation date: {staff_created}")
        
        # Check attendance records for this staff
        attendance_records = conn.execute('''
            SELECT date, time_in, time_out, status, created_at
            FROM attendance
            WHERE staff_id = ?
            ORDER BY date
        ''', (recent_staff['id'],)).fetchall()
        
        print(f"Total attendance records: {len(attendance_records)}")
        
        if attendance_records:
            print("\nAll attendance records:")
            for i, record in enumerate(attendance_records):
                record_date = datetime.datetime.strptime(record['date'], '%Y-%m-%d').date()
                is_historical = record_date < staff_created
                status_icon = "⚠️" if is_historical else "✅"
                print(f"  {status_icon} {record['date']}: {record['time_in']} - {record['time_out']} ({record['status']})")
                if i >= 10:  # Limit output
                    print(f"  ... and {len(attendance_records) - 10} more records")
                    break
            
            # Check for historical records
            historical_records = [r for r in attendance_records 
                                if datetime.datetime.strptime(r['date'], '%Y-%m-%d').date() < staff_created]
            
            if historical_records:
                print(f"\n⚠️  ISSUE FOUND: {len(historical_records)} attendance records BEFORE staff creation date!")
                print("Historical records (should not exist):")
                for record in historical_records[:5]:
                    print(f"  - {record['date']}: {record['time_in']} - {record['time_out']}")
                
                # Check if there's a pattern
                earliest_record = min(historical_records, key=lambda x: x['date'])
                latest_record = max(historical_records, key=lambda x: x['date'])
                print(f"\nHistorical records span from {earliest_record['date']} to {latest_record['date']}")
                
                return True  # Issue found
            else:
                print("\n✅ No historical attendance records found - this is correct!")
                return False  # No issue
        else:
            print("\n✅ No attendance records found for this staff - this is correct for new staff!")
            return False  # No issue
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        conn.close()

def check_for_auto_generation_code():
    """Check if there's code that automatically generates attendance"""
    print("\n=== Checking for Auto-Generation Code ===")
    
    files_to_check = [
        'app.py',
        'database.py',
        'zk_biometric.py'
    ]
    
    suspicious_patterns = [
        'INSERT INTO attendance',
        'generate.*attendance',
        'create.*attendance',
        'historical.*attendance',
        'previous.*attendance',
        'backfill.*attendance'
    ]
    
    found_issues = []
    
    for file_path in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            print(f"\nChecking {file_path}:")
            
            for i, line in enumerate(lines, 1):
                for pattern in suspicious_patterns:
                    if pattern.lower() in line.lower() and 'INSERT INTO attendance' in line:
                        print(f"  ⚠️  Line {i}: {line.strip()}")
                        found_issues.append((file_path, i, line.strip()))
                        
        except FileNotFoundError:
            print(f"  ❌ File {file_path} not found")
        except Exception as e:
            print(f"  ❌ Error reading {file_path}: {e}")
    
    if found_issues:
        print(f"\n⚠️  Found {len(found_issues)} potential auto-generation locations")
        return True
    else:
        print("\n✅ No automatic attendance generation code found")
        return False

def suggest_fixes():
    """Suggest fixes for the attendance issue"""
    print("\n=== Suggested Fixes ===")
    
    print("1. 🧹 Clean up historical records:")
    print("   - Delete attendance records that are older than staff creation date")
    print("   - Keep only attendance from staff creation date onwards")
    
    print("\n2. 🔍 Find the source:")
    print("   - Check if biometric device sync is creating historical records")
    print("   - Look for any initialization code that creates sample data")
    print("   - Check if there's any data migration or seeding happening")
    
    print("\n3. 🛡️ Prevent future issues:")
    print("   - Add validation to ensure attendance dates >= staff creation date")
    print("   - Add logging to track when attendance records are created")
    print("   - Review biometric sync process")

if __name__ == "__main__":
    print("🔍 Checking Attendance Issue for New Staff")
    print("=" * 60)
    
    # Check for the main issue
    has_historical_issue = check_attendance_issue()
    
    # Check for auto-generation code
    has_code_issue = check_for_auto_generation_code()
    
    # Provide suggestions
    if has_historical_issue or has_code_issue:
        suggest_fixes()
        
        print("\n" + "=" * 60)
        print("📊 ISSUE SUMMARY")
        print("=" * 60)
        
        if has_historical_issue:
            print("❌ CONFIRMED: New staff are getting historical attendance records")
        
        if has_code_issue:
            print("❌ FOUND: Code that might be creating attendance records")
        
        print("\n🔧 NEXT STEPS:")
        print("1. Clean up existing historical records")
        print("2. Find and fix the source of auto-generation")
        print("3. Add validation to prevent future issues")
        
    else:
        print("\n" + "=" * 60)
        print("✅ NO ISSUES FOUND")
        print("=" * 60)
        print("The attendance system appears to be working correctly.")
        print("New staff should only have attendance records from their creation date onwards.")
